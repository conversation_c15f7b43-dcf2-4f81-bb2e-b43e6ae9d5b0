@extends('layouts.frontend')

@section('title', 'Checkout')

@section('content')
<!-- Checkout Header -->
<section class="py-12 bg-gradient-to-r from-green-600 to-green-700">
    <div class="container mx-auto px-4">
        <div class="text-center text-white">
            <h1 class="text-4xl font-bold mb-4">Checkout</h1>
            <p class="text-xl opacity-90">Complete your order</p>
        </div>
    </div>
</section>

<!-- Checkout Content -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <form id="checkout-form" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            @csrf
            
            <!-- Checkout Form -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Shipping Address -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Shipping Address</h2>
                    
                    @if($addresses->where('type', 'shipping')->count() > 0 || $addresses->where('type', 'both')->count() > 0)
                        <!-- Existing Addresses -->
                        <div class="space-y-4 mb-6">
                            @foreach($addresses->whereIn('type', ['shipping', 'both']) as $address)
                            <label class="block">
                                <input type="radio" name="shipping_address_id" value="{{ $address->id }}" 
                                       class="sr-only address-radio" 
                                       {{ $address->is_default ? 'checked' : '' }}>
                                <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-500 transition-colors address-card {{ $address->is_default ? 'border-green-500 bg-green-50' : '' }}">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ $address->display_label }}</h3>
                                            <p class="text-gray-600">{{ $address->full_name }}</p>
                                            <p class="text-sm text-gray-500">{{ $address->formatted_address }}</p>
                                            @if($address->phone)
                                            <p class="text-sm text-gray-500">{{ $address->phone }}</p>
                                            @endif
                                        </div>
                                        @if($address->is_default)
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Default</span>
                                        @endif
                                    </div>
                                </div>
                            </label>
                            @endforeach
                        </div>
                    @endif
                    
                    <button type="button" onclick="showAddAddressModal('shipping')" 
                            class="text-green-600 hover:text-green-800 font-medium">
                        <i class="fas fa-plus mr-2"></i>Add New Address
                    </button>
                </div>
                
                <!-- Billing Address -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Billing Address</h2>
                    
                    <label class="flex items-center mb-4">
                        <input type="checkbox" id="same-as-shipping" class="mr-2" checked>
                        <span class="text-gray-700">Same as shipping address</span>
                    </label>
                    
                    <div id="billing-address-section" class="hidden">
                        @if($addresses->where('type', 'billing')->count() > 0 || $addresses->where('type', 'both')->count() > 0)
                            <!-- Existing Billing Addresses -->
                            <div class="space-y-4 mb-6">
                                @foreach($addresses->whereIn('type', ['billing', 'both']) as $address)
                                <label class="block">
                                    <input type="radio" name="billing_address_id" value="{{ $address->id }}" 
                                           class="sr-only billing-address-radio" 
                                           {{ $address->is_default ? 'checked' : '' }}>
                                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-500 transition-colors billing-address-card {{ $address->is_default ? 'border-green-500 bg-green-50' : '' }}">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">{{ $address->display_label }}</h3>
                                                <p class="text-gray-600">{{ $address->full_name }}</p>
                                                <p class="text-sm text-gray-500">{{ $address->formatted_address }}</p>
                                                @if($address->phone)
                                                <p class="text-sm text-gray-500">{{ $address->phone }}</p>
                                                @endif
                                            </div>
                                            @if($address->is_default)
                                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Default</span>
                                            @endif
                                        </div>
                                    </div>
                                </label>
                                @endforeach
                            </div>
                        @endif
                        
                        <button type="button" onclick="showAddAddressModal('billing')" 
                                class="text-green-600 hover:text-green-800 font-medium">
                            <i class="fas fa-plus mr-2"></i>Add New Billing Address
                        </button>
                    </div>
                </div>
                
                <!-- Payment Method -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Payment Method</h2>
                    
                    <div class="space-y-4">
                        <!-- Manual Payment -->
                        <label class="block">
                            <input type="radio" name="payment_method" value="manual" class="sr-only payment-radio" checked>
                            <div class="border-2 border-green-500 bg-green-50 rounded-lg p-4 cursor-pointer hover:border-green-600 transition-colors payment-card">
                                <div class="flex items-center">
                                    <i class="fas fa-money-bill-wave text-green-600 text-xl mr-4"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Manual Payment</h3>
                                        <p class="text-sm text-gray-600">Pay via bank transfer or cash on delivery</p>
                                    </div>
                                </div>
                                <div class="mt-3 p-3 bg-green-100 rounded-lg">
                                    <p class="text-sm text-green-800">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        Payment instructions will be sent to your email after placing the order.
                                    </p>
                                </div>
                            </div>
                        </label>
                        
                        <!-- PayPal -->
                        <label class="block">
                            <input type="radio" name="payment_method" value="paypal" class="sr-only payment-radio">
                            <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-500 transition-colors payment-card">
                                <div class="flex items-center">
                                    <i class="fab fa-paypal text-blue-600 text-xl mr-4"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">PayPal</h3>
                                        <p class="text-sm text-gray-600">Pay securely with your PayPal account</p>
                                    </div>
                                </div>
                                <div class="mt-3 p-3 bg-blue-50 rounded-lg hidden payment-details" data-method="paypal">
                                    <p class="text-sm text-blue-800">
                                        <i class="fab fa-paypal mr-2"></i>
                                        You will be redirected to PayPal to complete your payment securely.
                                    </p>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Stripe -->
                        <label class="block">
                            <input type="radio" name="payment_method" value="stripe" class="sr-only payment-radio">
                            <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-500 transition-colors payment-card">
                                <div class="flex items-center">
                                    <i class="fas fa-credit-card text-purple-600 text-xl mr-4"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Credit/Debit Card</h3>
                                        <p class="text-sm text-gray-600">Pay with Visa, Mastercard, or American Express</p>
                                    </div>
                                </div>
                                <div class="mt-3 p-3 bg-purple-50 rounded-lg hidden payment-details" data-method="stripe">
                                    <p class="text-sm text-purple-800">
                                        <i class="fas fa-shield-alt mr-2"></i>
                                        Secure payment processing powered by Stripe. Your card details are encrypted.
                                    </p>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Order Notes -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Order Notes (Optional)</h2>
                    <textarea name="notes" rows="4" 
                              class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Any special instructions for your order..."></textarea>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-lg p-6 sticky top-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Order Summary</h3>
                    
                    <!-- Cart Items -->
                    <div class="space-y-4 mb-6 max-h-64 overflow-y-auto">
                        @foreach($cart->items as $item)
                        <div class="flex items-center space-x-3">
                            @if($item->product->featured_image_url)
                                <img src="{{ $item->product->featured_image_url }}"
                                     alt="{{ $item->product->name }}"
                                     class="w-12 h-12 object-cover rounded">
                            @else
                                <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-box text-gray-400 text-sm"></i>
                                </div>
                            @endif
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $item->product->name }}</h4>
                                <p class="text-sm text-gray-600">Qty: {{ $item->quantity }}</p>
                            </div>
                            <div class="text-sm font-semibold text-gray-900">
                                ${{ $item->formatted_total_price }}
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Totals -->
                    <div class="space-y-3 mb-6 border-t border-gray-200 pt-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-semibold">${{ number_format($cart->subtotal, 2) }}</span>
                        </div>
                        
                        @if($cart->tax_amount > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tax</span>
                            <span class="font-semibold">${{ number_format($cart->tax_amount, 2) }}</span>
                        </div>
                        @endif
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Shipping</span>
                            <span class="font-semibold text-green-600">Free</span>
                        </div>
                        
                        @if($cart->discount_amount > 0)
                        <div class="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span class="font-semibold">-${{ number_format($cart->discount_amount, 2) }}</span>
                        </div>
                        @endif
                    </div>
                    
                    <div class="border-t border-gray-200 pt-4 mb-6">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span class="text-green-600">${{ number_format($cart->total_amount, 2) }}</span>
                        </div>
                    </div>
                    
                    <!-- Place Order Button -->
                    <button type="submit" id="place-order-btn"
                            class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold transition-colors">
                        Place Order
                    </button>
                    
                    <!-- Security Info -->
                    <div class="mt-6 text-center">
                        <div class="flex items-center justify-center text-sm text-gray-600 mb-2">
                            <i class="fas fa-lock mr-2 text-green-600"></i>
                            SSL Secured Checkout
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-600">
                            <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                            Your information is protected
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Add Address Modal -->
<div id="add-address-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-bold text-gray-900">Add New Address</h3>
                <button onclick="closeAddAddressModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="add-address-form">
                @csrf
                <input type="hidden" id="address-type" name="type" value="shipping">
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" name="first_name" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" name="last_name" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Label (Optional)</label>
                    <input type="text" name="label" placeholder="e.g., Home, Office" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 1</label>
                    <input type="text" name="address_line_1" required 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 2 (Optional)</label>
                    <input type="text" name="address_line_2" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input type="text" name="city" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">State</label>
                        <input type="text" name="state" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                        <input type="text" name="postal_code" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                        <input type="text" name="country" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                    <input type="tel" name="phone" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_default" class="mr-2">
                        <span class="text-sm text-gray-700">Set as default address</span>
                    </label>
                </div>
                
                <div class="flex gap-4">
                    <button type="button" onclick="closeAddAddressModal()" 
                            class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        Add Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle address selection
    const addressRadios = document.querySelectorAll('.address-radio');
    addressRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove active state from all cards
            document.querySelectorAll('.address-card').forEach(card => {
                card.classList.remove('border-green-500', 'bg-green-50');
                card.classList.add('border-gray-200');
            });

            // Add active state to selected card
            const card = this.closest('.address-card');
            card.classList.remove('border-gray-200');
            card.classList.add('border-green-500', 'bg-green-50');
        });
    });

    // Handle billing address selection
    const billingRadios = document.querySelectorAll('.billing-address-radio');
    billingRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove active state from all cards
            document.querySelectorAll('.billing-address-card').forEach(card => {
                card.classList.remove('border-green-500', 'bg-green-50');
                card.classList.add('border-gray-200');
            });

            // Add active state to selected card
            const card = this.closest('.billing-address-card');
            card.classList.remove('border-gray-200');
            card.classList.add('border-green-500', 'bg-green-50');
        });
    });

    // Handle payment method selection
    const paymentRadios = document.querySelectorAll('.payment-radio');
    paymentRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove active state from all cards
            document.querySelectorAll('.payment-card').forEach(card => {
                card.classList.remove('border-green-500', 'bg-green-50');
                card.classList.add('border-gray-200');
            });

            // Hide all payment details
            document.querySelectorAll('.payment-details').forEach(detail => {
                detail.classList.add('hidden');
            });

            // Add active state to selected card
            const card = this.closest('.payment-card');
            card.classList.remove('border-gray-200');
            card.classList.add('border-green-500', 'bg-green-50');

            // Show payment details for selected method
            const paymentDetails = card.querySelector('.payment-details');
            if (paymentDetails) {
                paymentDetails.classList.remove('hidden');
            }
        });
    });

    // Handle same as shipping checkbox
    const sameAsShippingCheckbox = document.getElementById('same-as-shipping');
    const billingAddressSection = document.getElementById('billing-address-section');

    if (sameAsShippingCheckbox && billingAddressSection) {
        sameAsShippingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                billingAddressSection.classList.add('hidden');
            } else {
                billingAddressSection.classList.remove('hidden');
            }
        });
    }

    // Handle checkout form submission
    const checkoutForm = document.getElementById('checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // If same as shipping is checked, use shipping address for billing
        if (sameAsShippingCheckbox.checked) {
            data.billing_address_id = data.shipping_address_id;
        }

        const button = document.getElementById('place-order-btn');
        const originalText = button.innerHTML;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing Order...';

        fetch('/checkout/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('HTTP error! status: ' + response.status);
            }
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Server returned non-JSON response');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showNotification('Order placed successfully!', 'success');

                // Handle payment redirect
                if (data.payment_response && data.payment_response.redirect) {
                    setTimeout(() => {
                        window.location.href = data.payment_response.redirect;
                    }, 1000);
                } else {
                    // Redirect to order confirmation
                    setTimeout(() => {
                        window.location.href = '/checkout/confirmation/' + data.order_id;
                    }, 1000);
                }
            } else {
                showNotification(data.message, 'error');

                // Reset button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');

            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    // Handle add address form submission
    const addAddressForm = document.getElementById('add-address-form');
    if (addAddressForm) {
        addAddressForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            fetch('/addresses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Server returned non-JSON response');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showNotification('Address added successfully!', 'success');
                    closeAddAddressModal();

                    // Reload page to show new address
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showNotification(data.message || 'Failed to add address', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred. Please try again.', 'error');
            });
        });
    }
});

// Show add address modal
function showAddAddressModal(type) {
    document.getElementById('address-type').value = type;
    document.getElementById('add-address-modal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

// Close add address modal
function closeAddAddressModal() {
    document.getElementById('add-address-modal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    document.getElementById('add-address-form').reset();
}
</script>
@endpush
