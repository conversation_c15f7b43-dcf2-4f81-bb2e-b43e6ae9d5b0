@php
    $seoService = app(\App\Services\SeoLocalizationService::class);
    $currentLocale = $seoService->getCurrentLocale();
    $language = explode('-', $currentLocale)[0];
@endphp
<!DOCTYPE html>
<html lang="{{ $language }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Locale and Language Meta Tags -->
    <meta http-equiv="content-language" content="{{ $currentLocale }}">
    <meta name="language" content="{{ $language }}">

    <!-- SEO Meta Tags with Localization -->
    <title>@yield('title', $seoService->getLocalizedMetaTitle($currentLocale, ($siteSettings['site_name'] ?? 'Atrix Logistics') . ' - Professional Logistics Solutions'))</title>
    <meta name="description" content="@yield('description', $seoService->getLocalizedMetaDescription($currentLocale, $siteSettings['site_description'] ?? 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.'))">
    <meta name="keywords" content="@yield('keywords', $siteSettings['site_keywords'] ?? 'logistics, freight, shipping, warehousing, supply chain, automotive parts, steel products, containers')">
    <meta name="author" content="{{ $seoService->getLocalizedOrganizationName($currentLocale) }}">

    <!-- Hreflang Tags for International SEO -->
    <x-seo-hreflang />

    <!-- Open Graph Meta Tags with Localization -->
    <meta property="og:title" content="@yield('title', $seoService->getLocalizedMetaTitle($currentLocale, ($siteSettings['site_name'] ?? 'Atrix Logistics') . ' - Professional Logistics Solutions'))">
    <meta property="og:description" content="@yield('description', $seoService->getLocalizedMetaDescription($currentLocale, $siteSettings['site_description'] ?? 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.'))">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:locale" content="{{ str_replace('-', '_', $currentLocale) }}">
    @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
    <meta property="og:image" content="{{ Storage::url($siteSettings['site_logo']) }}">
    @endif
    <meta property="og:site_name" content="{{ $seoService->getLocalizedOrganizationName($currentLocale) }}">

    <!-- Twitter Card Meta Tags with Localization -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('title', $seoService->getLocalizedMetaTitle($currentLocale, ($siteSettings['site_name'] ?? 'Atrix Logistics') . ' - Professional Logistics Solutions'))">
    <meta name="twitter:description" content="@yield('description', $seoService->getLocalizedMetaDescription($currentLocale, $siteSettings['site_description'] ?? 'Professional logistics and freight solutions provider.'))">
    @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
    <meta name="twitter:image" content="{{ Storage::url($siteSettings['site_logo']) }}">
    @endif

    <!-- Favicon -->
    @if(isset($siteSettings['site_favicon']) && $siteSettings['site_favicon'])
    <link rel="icon" type="image/x-icon" href="{{ Storage::url($siteSettings['site_favicon']) }}">
    @else
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'heading': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'earth': {
                            100: '#f5f1eb',
                            200: '#e8dcc6',
                            300: '#dbc7a1',
                            400: '#ceb27c',
                            500: '#b08d57',
                            600: '#9a7a4a',
                            700: '#82653f',
                            800: '#6b5034',
                            900: '#543b29',
                        }
                    },
                    maxWidth: {
                        '8xl': '88rem',
                        '9xl': '96rem',
                    }
                },
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        'sm': '640px',
                        'md': '768px',
                        'lg': '1024px',
                        'xl': '1280px',
                        '2xl': '1400px',
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom Styles -->
    <style>
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
        }

        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #16a34a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #15803d;
        }
    </style>

    @stack('styles')
</head>

<body class="font-sans antialiased bg-white">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50">
        Skip to main content
    </a>

    <!-- Header -->
    @include('layouts.partials.frontend.header')

    <!-- Main Content -->
    <main id="main-content">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('layouts.partials.frontend.footer')

    <!-- Quote Modal -->
    @include('components.quote-modal')

    <!-- Live Chat Widget -->
    @if(isset($siteSettings['live_chat_enabled']) && $siteSettings['live_chat_enabled'])
        @include('components.live-chat-widget')
    @endif

    <!-- WhatsApp Float Button -->
    @include('components.whatsapp-float')

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-28 right-6 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible z-40">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const hamburger = document.querySelector('.hamburger');

            if (mobileMenu && mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                if (hamburger) hamburger.classList.add('active');
                document.body.classList.add('overflow-hidden');
            } else if (mobileMenu) {
                mobileMenu.classList.add('hidden');
                if (hamburger) hamburger.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Mobile dropdown toggle
        function toggleMobileDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId + '-dropdown');
            const icon = document.getElementById(dropdownId + '-icon');

            if (dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                dropdown.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }

        // Quote modal functions
        function openQuoteModal() {
            const modal = document.getElementById('quoteModal');
            if (modal) {
                // Use Bootstrap modal if available
                if (typeof bootstrap !== 'undefined') {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    // Fallback for custom modal
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                }
            }
        }

        function closeQuoteModal() {
            const modal = document.getElementById('quoteModal');
            if (modal) {
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Scroll animations
        function initScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.animate-on-scroll').forEach((el) => {
                observer.observe(el);
            });
        }

        // Back to top button
        function initBackToTop() {
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });

            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initScrollAnimations();
            initBackToTop();
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.getElementById('mobile-menu');
            const hamburger = document.querySelector('.hamburger');
            const mobileMenuButton = document.querySelector('[onclick="toggleMobileMenu()"]');

            if (mobileMenu && mobileMenuButton && !mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target) && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
                if (hamburger) hamburger.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
        });
    </script>

    <!-- Currency Helper Script -->
    <script>
        // Global currency settings
        window.currencySettings = {
            symbol: '@currencySymbol',
            code: '@currencyCode',
            position: '{{ \App\Helpers\CurrencyHelper::getPosition() }}',
            decimalPlaces: {{ \App\Helpers\CurrencyHelper::getDecimalPlaces() }},
            thousandsSeparator: '{{ \App\Helpers\CurrencyHelper::getThousandsSeparator() }}',
            decimalSeparator: '{{ \App\Helpers\CurrencyHelper::getDecimalSeparator() }}'
        };

        // Currency formatting function
        function formatCurrency(amount, showSymbol = true) {
            if (amount === null || amount === undefined || amount === '') {
                amount = 0;
            }

            amount = parseFloat(amount);

            // Format the number
            const formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: window.currencySettings.decimalPlaces,
                maximumFractionDigits: window.currencySettings.decimalPlaces
            });

            if (!showSymbol) {
                return formattedAmount;
            }

            // Add currency symbol
            if (window.currencySettings.position === 'before') {
                return window.currencySettings.symbol + formattedAmount;
            } else {
                return formattedAmount + ' ' + window.currencySettings.symbol;
            }
        }

        // Parse currency string to float
        function parseCurrency(currencyString) {
            if (!currencyString) return 0;

            // Remove currency symbol and spaces
            let cleaned = currencyString.toString()
                .replace(window.currencySettings.symbol, '')
                .replace(/\s/g, '');

            // Replace thousands separator
            if (window.currencySettings.thousandsSeparator !== ',') {
                cleaned = cleaned.replace(new RegExp('\\' + window.currencySettings.thousandsSeparator, 'g'), '');
            } else {
                cleaned = cleaned.replace(/,/g, '');
            }

            // Replace decimal separator with dot
            if (window.currencySettings.decimalSeparator !== '.') {
                cleaned = cleaned.replace(window.currencySettings.decimalSeparator, '.');
            }

            return parseFloat(cleaned) || 0;
        }
    </script>

    @stack('scripts')

    <!-- Notification System for Mockups -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
    // Global notification function for mockups
    function showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');

        const bgColor = type === 'success' ? 'bg-green-500' :
                       type === 'error' ? 'bg-red-500' :
                       type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

        notification.className = `${bgColor} text-white px-4 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    // Update cart count on page load
    document.addEventListener('DOMContentLoaded', function() {
        @auth
        updateCartCount();
        @endauth
    });

    // Update cart count function
    function updateCartCount() {
        fetch('/cart/count')
            .then(response => response.json())
            .then(data => {
                const cartCountElement = document.querySelector('.cart-count');
                if (cartCountElement) {
                    cartCountElement.textContent = data.count || 0;

                    // Hide/show cart count badge
                    if (data.count > 0) {
                        cartCountElement.classList.remove('hidden');
                    } else {
                        cartCountElement.classList.add('hidden');
                    }
                }
            })
            .catch(error => console.error('Error updating cart count:', error));
    }
    </script>
</body>
</html>
