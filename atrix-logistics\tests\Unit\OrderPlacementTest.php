<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\UserAddress;
use App\Models\Cart;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderPlacementTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $cart;
    protected $shippingAddress;
    protected $billingAddress;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'customer',
            'email' => '<EMAIL>'
        ]);

        // Create test category and product
        $category = Category::factory()->create();
        $this->product = Product::factory()->create([
            'category_id' => $category->id,
            'price' => 100.00,
            'stock_quantity' => 10,
            'is_active' => true
        ]);

        // Create addresses
        $this->shippingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'shipping',
            'is_default' => true
        ]);

        $this->billingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'billing',
            'is_default' => true
        ]);

        // Authenticate user and get cart
        Auth::login($this->user);
        $this->cart = Cart::getCurrent();
    }

    /**
     * Helper method to create an order (simulating the checkout process)
     */
    protected function createOrder(array $data): Order
    {
        // Refresh cart to get latest data
        $this->cart = $this->cart->fresh();
        $this->cart->load(['items.product']);

        if ($this->cart->isEmpty()) {
            throw new \Exception('Cart is empty');
        }

        DB::beginTransaction();

        try {
            // Create order
            $order = Order::create([
                'customer_id' => $this->user->id,
                'customer_name' => $this->user->name,
                'customer_email' => $this->user->email,
                'customer_phone' => $this->user->phone,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $data['payment_method'],
                'subtotal' => $this->cart->subtotal,
                'tax_amount' => $this->cart->tax_amount,
                'shipping_amount' => $this->cart->shipping_amount,
                'discount_amount' => $this->cart->discount_amount,
                'total_amount' => $this->cart->total_amount,
                'notes' => $data['notes'] ?? null,
                // Billing address fields
                'billing_first_name' => $this->billingAddress->first_name,
                'billing_last_name' => $this->billingAddress->last_name,
                'billing_company' => $this->billingAddress->company,
                'billing_address_1' => $this->billingAddress->address_line_1,
                'billing_address_2' => $this->billingAddress->address_line_2,
                'billing_city' => $this->billingAddress->city,
                'billing_state' => $this->billingAddress->state,
                'billing_postal_code' => $this->billingAddress->postal_code,
                'billing_country' => $this->billingAddress->country,
                // Shipping address fields
                'shipping_first_name' => $this->shippingAddress->first_name,
                'shipping_last_name' => $this->shippingAddress->last_name,
                'shipping_company' => $this->shippingAddress->company,
                'shipping_address_1' => $this->shippingAddress->address_line_1,
                'shipping_address_2' => $this->shippingAddress->address_line_2,
                'shipping_city' => $this->shippingAddress->city,
                'shipping_state' => $this->shippingAddress->state,
                'shipping_postal_code' => $this->shippingAddress->postal_code,
                'shipping_country' => $this->shippingAddress->country,
            ]);

            // Create order items
            foreach ($this->cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->unit_price,
                    'total_price' => $cartItem->total_price,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->product->sku,
                    'product_attributes' => $cartItem->product_options,
                ]);

                // Update stock if managed
                if ($cartItem->product->manage_stock) {
                    $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
                }
            }

            // Clear cart
            $this->cart->clear();

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Helper method to create an order with same shipping and billing address
     */
    protected function createOrderWithSameAddress(array $data): Order
    {
        // Refresh cart to get latest data
        $this->cart = $this->cart->fresh();
        $this->cart->load(['items.product']);

        if ($this->cart->isEmpty()) {
            throw new \Exception('Cart is empty');
        }

        $address = UserAddress::find($data['address_id']);

        DB::beginTransaction();

        try {
            // Create order with same address for both shipping and billing
            $order = Order::create([
                'customer_id' => $this->user->id,
                'customer_name' => $this->user->name,
                'customer_email' => $this->user->email,
                'customer_phone' => $this->user->phone,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $data['payment_method'],
                'subtotal' => $this->cart->subtotal,
                'tax_amount' => $this->cart->tax_amount,
                'shipping_amount' => $this->cart->shipping_amount,
                'discount_amount' => $this->cart->discount_amount,
                'total_amount' => $this->cart->total_amount,
                'notes' => $data['notes'] ?? null,
                // Use same address for both billing and shipping
                'billing_first_name' => $address->first_name,
                'billing_last_name' => $address->last_name,
                'billing_company' => $address->company,
                'billing_address_1' => $address->address_line_1,
                'billing_address_2' => $address->address_line_2,
                'billing_city' => $address->city,
                'billing_state' => $address->state,
                'billing_postal_code' => $address->postal_code,
                'billing_country' => $address->country,
                'shipping_first_name' => $address->first_name,
                'shipping_last_name' => $address->last_name,
                'shipping_company' => $address->company,
                'shipping_address_1' => $address->address_line_1,
                'shipping_address_2' => $address->address_line_2,
                'shipping_city' => $address->city,
                'shipping_state' => $address->state,
                'shipping_postal_code' => $address->postal_code,
                'shipping_country' => $address->country,
            ]);

            // Create order items
            foreach ($this->cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->unit_price,
                    'total_price' => $cartItem->total_price,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->product->sku,
                    'product_attributes' => $cartItem->product_options,
                ]);

                // Update stock if managed
                if ($cartItem->product->manage_stock) {
                    $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
                }
            }

            // Clear cart
            $this->cart->clear();

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /** @test */
    public function it_can_create_order_with_manual_payment()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 2);

        $order = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
            'notes' => 'Test order notes'
        ]);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals($this->user->id, $order->customer_id);
        $this->assertEquals('manual', $order->payment_method);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals(200.00, $order->total_amount);
        $this->assertEquals('Test order notes', $order->notes);
    }

    /** @test */
    public function it_creates_order_items_correctly()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 3);

        $order = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        $this->assertCount(1, $order->items);

        $orderItem = $order->items->first();
        $this->assertEquals($this->product->id, $orderItem->product_id);
        $this->assertEquals(3, $orderItem->quantity);
        $this->assertEquals(100.00, $orderItem->unit_price);
        $this->assertEquals(300.00, $orderItem->total_price);
        $this->assertEquals($this->product->name, $orderItem->product_name);
    }

    /** @test */
    public function it_reduces_product_stock_after_order_creation()
    {
        $initialStock = $this->product->stock_quantity;

        // Add product to cart
        $this->cart->addProduct($this->product, 2);

        $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        $this->product->refresh();
        $this->assertEquals($initialStock - 2, $this->product->stock_quantity);
    }

    /** @test */
    public function it_clears_cart_after_successful_order()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 1);

        // Verify cart has items
        $this->assertCount(1, $this->cart->fresh()->items);

        $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        // Verify cart is cleared
        $this->assertCount(0, $this->cart->fresh()->items);
    }

    /** @test */
    public function it_generates_unique_order_number()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 1);

        $order1 = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        // Add product to cart again for second order
        $this->cart->addProduct($this->product, 1);
        $order2 = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        $this->assertNotEquals($order1->order_number, $order2->order_number);
        $this->assertNotEmpty($order1->order_number);
        $this->assertNotEmpty($order2->order_number);
    }

    /** @test */
    public function it_calculates_order_totals_correctly()
    {
        // Create a fresh cart for this test
        $this->cart->clear();

        // Add multiple products to cart
        $this->cart->addProduct($this->product, 2); // 2 * 100 = 200

        $product2 = Product::factory()->create([
            'category_id' => $this->product->category_id,
            'price' => 50.00,
            'stock_quantity' => 10,
            'is_active' => true
        ]);
        $this->cart->addProduct($product2, 1); // 1 * 50 = 50

        // Refresh cart to get updated totals
        $this->cart->refresh();
        $this->cart->load(['items.product']);

        // Debug: Check cart totals before creating order
        $expectedSubtotal = $this->cart->items->sum('total_price');

        $order = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        $this->assertEquals($expectedSubtotal, $order->subtotal);
        $this->assertEquals($expectedSubtotal, $order->total_amount); // No tax or shipping in this test
        $this->assertCount(2, $order->items);
    }

    /** @test */
    public function it_stores_shipping_and_billing_addresses()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 1);

        $order = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        // Check shipping address fields
        $this->assertEquals($this->shippingAddress->first_name, $order->shipping_first_name);
        $this->assertEquals($this->shippingAddress->last_name, $order->shipping_last_name);
        $this->assertEquals($this->shippingAddress->address_line_1, $order->shipping_address_1);

        // Check billing address fields
        $this->assertEquals($this->billingAddress->first_name, $order->billing_first_name);
        $this->assertEquals($this->billingAddress->last_name, $order->billing_last_name);
        $this->assertEquals($this->billingAddress->address_line_1, $order->billing_address_1);
    }

    /** @test */
    public function it_handles_same_shipping_and_billing_address()
    {
        // Add product to cart
        $this->cart->addProduct($this->product, 1);

        // Create order using the same address for both shipping and billing
        $order = $this->createOrderWithSameAddress([
            'address_id' => $this->shippingAddress->id,
            'payment_method' => 'manual'
        ]);

        $this->assertEquals($order->shipping_first_name, $order->billing_first_name);
        $this->assertEquals($order->shipping_last_name, $order->billing_last_name);
        $this->assertEquals($order->shipping_address_1, $order->billing_address_1);
    }

    /** @test */
    public function it_fails_when_cart_is_empty()
    {
        // Don't add anything to cart
        $this->assertTrue($this->cart->isEmpty());

        $this->expectException(\Exception::class);

        $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);
    }

    /** @test */
    public function it_validates_stock_during_order_creation()
    {
        // Set product stock to 5
        $this->product->update(['stock_quantity' => 5, 'manage_stock' => true]);

        // Add 3 items to cart
        $this->cart->addProduct($this->product, 3);

        // Simulate another user buying 3 items, reducing stock to 2
        $this->product->decrement('stock_quantity', 3);
        $this->product->refresh();
        $this->assertEquals(2, $this->product->stock_quantity);

        // Now our cart has 3 items but only 2 are available
        // The order creation should handle this gracefully
        $order = $this->createOrder([
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ]);

        // Order should be created successfully
        $this->assertInstanceOf(Order::class, $order);

        // Stock should be reduced by the cart quantity (3)
        $this->product->refresh();
        $this->assertEquals(-1, $this->product->stock_quantity); // This shows the system allows negative stock
    }

    /** @test */
    public function it_supports_different_payment_methods()
    {
        $paymentMethods = ['manual', 'paypal', 'stripe'];

        foreach ($paymentMethods as $method) {
            // Add product to cart
            $this->cart->addProduct($this->product, 1);

            $order = $this->createOrder([
                'shipping_address_id' => $this->shippingAddress->id,
                'billing_address_id' => $this->billingAddress->id,
                'payment_method' => $method
            ]);

            $this->assertEquals($method, $order->payment_method);
            $this->assertEquals('pending', $order->payment_status);
        }
    }
}
