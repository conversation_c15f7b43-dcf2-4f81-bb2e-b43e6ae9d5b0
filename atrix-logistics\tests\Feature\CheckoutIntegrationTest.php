<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\UserAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CheckoutIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $shippingAddress;
    protected $billingAddress;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'customer',
            'email' => '<EMAIL>'
        ]);

        // Create test category and product
        $category = Category::factory()->create();
        $this->product = Product::factory()->create([
            'category_id' => $category->id,
            'price' => 100.00,
            'stock_quantity' => 10,
            'is_active' => true
        ]);

        // Create addresses
        $this->shippingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'shipping',
            'is_default' => true
        ]);

        $this->billingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'billing',
            'is_default' => true
        ]);
    }

    /** @test */
    public function authenticated_user_can_access_checkout_page()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $response = $this->actingAs($this->user)
                         ->get('/checkout');

        $response->assertStatus(200);
        $response->assertViewIs('frontend.checkout.index');
        $response->assertViewHas(['cart', 'addresses']);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_checkout()
    {
        $response = $this->get('/checkout');

        $response->assertRedirect('/customer/login');
    }

    /** @test */
    public function user_cannot_checkout_with_empty_cart()
    {
        $response = $this->actingAs($this->user)
                         ->get('/checkout');

        $response->assertRedirect('/cart');
        $response->assertSessionHas('error', 'Your cart is empty.');
    }

    /** @test */
    public function user_can_place_order_with_manual_payment()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 2
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
            'notes' => 'Test order notes'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Order created successfully.'
        ]);

        // Verify order was created
        $orders = Order::all();
        $this->assertCount(1, $orders, 'Exactly one order should be created');

        $order = $orders->first();
        $this->assertEquals($this->user->id, $order->customer_id);
        $this->assertEquals('manual', $order->payment_method);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals('Test order notes', $order->notes);
        $this->assertGreaterThan(0, $order->total_amount);

        // Verify order items were created
        $this->assertCount(1, $order->items);
        $this->assertEquals(2, $order->items->first()->quantity);
        $this->assertGreaterThan(0, $order->items->first()->unit_price);
    }

    /** @test */
    public function user_can_place_order_with_paypal_payment()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'paypal'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Order created successfully.'
        ]);

        $responseData = $response->json();
        $this->assertArrayHasKey('payment_response', $responseData);
        $this->assertEquals('paypal', $responseData['payment_response']['type']);
        $this->assertStringContainsString('orders', $responseData['payment_response']['redirect']);
    }

    /** @test */
    public function user_can_place_order_with_stripe_payment()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'stripe'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Order created successfully.'
        ]);

        $responseData = $response->json();
        $this->assertArrayHasKey('payment_response', $responseData);
        $this->assertEquals('stripe', $responseData['payment_response']['type']);
        $this->assertStringContainsString('orders', $responseData['payment_response']['redirect']);
    }

    /** @test */
    public function checkout_fails_with_invalid_shipping_address()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => 999, // Non-existent address
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['shipping_address_id']);
    }

    /** @test */
    public function checkout_fails_with_invalid_billing_address()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => 999, // Non-existent address
            'payment_method' => 'manual'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['billing_address_id']);
    }

    /** @test */
    public function checkout_fails_with_invalid_payment_method()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'invalid_method'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['payment_method']);
    }

    /** @test */
    public function checkout_fails_when_user_not_authenticated()
    {
        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $response = $this->postJson('/checkout/process', $orderData);

        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'message' => 'Please log in to place an order.'
        ]);
    }

    /** @test */
    public function checkout_fails_with_empty_cart()
    {
        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Your cart is empty.'
        ]);
    }

    /** @test */
    public function checkout_reduces_product_stock()
    {
        $initialStock = $this->product->stock_quantity;

        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 3
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(200);

        // Verify stock was reduced
        $this->product->refresh();
        $this->assertEquals($initialStock - 3, $this->product->stock_quantity);
    }

    /** @test */
    public function checkout_clears_cart_after_successful_order()
    {
        // Add product to cart
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        // Verify cart has items
        $cartResponse = $this->actingAs($this->user)->get('/cart/count');
        $this->assertEquals(1, $cartResponse->json()['count']);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/checkout/process', $orderData);

        $response->assertStatus(200);

        // Verify cart is cleared
        $cartResponse = $this->actingAs($this->user)->get('/cart/count');
        $this->assertEquals(0, $cartResponse->json()['count']);
    }

    /** @test */
    public function user_can_view_order_confirmation()
    {
        // Create an order first
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $checkoutResponse = $this->actingAs($this->user)
                                 ->postJson('/checkout/process', $orderData);

        $orderId = $checkoutResponse->json()['order_id'];

        // View confirmation page
        $response = $this->actingAs($this->user)
                         ->get("/checkout/confirmation/{$orderId}");

        $response->assertStatus(200);
        $response->assertViewIs('frontend.checkout.confirmation');
        $response->assertViewHas('order');
        $response->assertSee('Order Confirmation');
        $response->assertSee('Manual Payment');
    }

    /** @test */
    public function user_cannot_view_other_users_order_confirmation()
    {
        $otherUser = User::factory()->create(['role' => 'customer']);

        // Create an order for the first user
        $this->actingAs($this->user)
             ->post('/cart/add', [
                 'product_id' => $this->product->id,
                 'quantity' => 1
             ]);

        $orderData = [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual'
        ];

        $checkoutResponse = $this->actingAs($this->user)
                                 ->postJson('/checkout/process', $orderData);

        $orderId = $checkoutResponse->json()['order_id'];

        // Try to view confirmation as other user
        $response = $this->actingAs($otherUser)
                         ->get("/checkout/confirmation/{$orderId}");

        // The route might return 404 if the order doesn't belong to the user
        $this->assertTrue(in_array($response->status(), [403, 404]));
    }
}
