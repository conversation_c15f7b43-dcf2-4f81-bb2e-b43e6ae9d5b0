<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order_number',
        'customer_id',
        'status',
        'customer_name',
        'customer_email',
        'customer_phone',
        'billing_first_name',
        'billing_last_name',
        'billing_company',
        'billing_address_1',
        'billing_address_2',
        'billing_city',
        'billing_state',
        'billing_postal_code',
        'billing_country',
        'shipping_first_name',
        'shipping_last_name',
        'shipping_company',
        'shipping_address_1',
        'shipping_address_2',
        'shipping_city',
        'shipping_state',
        'shipping_postal_code',
        'shipping_country',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'payment_status',
        'payment_method',
        'payment_reference',
        'paid_at',
        'shipping_method',
        'tracking_number',
        'shipped_at',
        'delivered_at',
        'notes',
        'admin_notes',
        'metadata',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeShipped($query)
    {
        return $query->where('status', 'shipped');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('payment_status', ['pending', 'failed']);
    }

    /**
     * Accessors & Mutators
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->status));
    }

    public function getFormattedPaymentStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->payment_status));
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'secondary',
            'confirmed' => 'info',
            'processing' => 'warning',
            'shipped' => 'primary',
            'delivered' => 'success',
            'cancelled' => 'danger',
            'refunded' => 'dark',
            default => 'secondary'
        };
    }

    public function getPaymentStatusBadgeColorAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'warning',
            'paid' => 'success',
            'partially_paid' => 'info',
            'refunded' => 'secondary',
            'failed' => 'danger',
            default => 'secondary'
        };
    }

    public function getBillingAddressAttribute(): string
    {
        return trim(implode(', ', array_filter([
            $this->billing_address_1,
            $this->billing_address_2,
            $this->billing_city,
            $this->billing_state,
            $this->billing_postal_code,
            $this->billing_country
        ])));
    }

    public function getShippingAddressAttribute(): string
    {
        return trim(implode(', ', array_filter([
            $this->shipping_address_1,
            $this->shipping_address_2,
            $this->shipping_city,
            $this->shipping_state,
            $this->shipping_postal_code,
            $this->shipping_country
        ])));
    }

    /**
     * Helper Methods
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    public function isShipped(): bool
    {
        return $this->status === 'shipped';
    }

    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    public function canBeShipped(): bool
    {
        return in_array($this->status, ['confirmed', 'processing']);
    }

    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Customer Helper Methods
     */
    public function canBeTracked(): bool
    {
        return !empty($this->tracking_number) && in_array($this->status, ['shipped', 'delivered']);
    }

    public function needsSupport(): bool
    {
        return in_array($this->status, ['pending', 'cancelled']) ||
               $this->payment_status === 'failed' ||
               $this->created_at->diffInDays() > 7;
    }

    public function canBeReordered(): bool
    {
        return $this->status === 'delivered' && $this->items->count() > 0;
    }

    public function canBePaid(): bool
    {
        return !$this->isPaid() && !$this->isCancelled() && $this->total_amount > 0;
    }

    public function needsPayment(): bool
    {
        return $this->canBePaid() && in_array($this->payment_status, ['pending', 'failed']);
    }

    /**
     * Static Methods
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        $orderNumber = $prefix . $timestamp . $random;

        // Ensure uniqueness
        $counter = 1;
        $originalOrderNumber = $orderNumber;
        while (static::where('order_number', $orderNumber)->exists()) {
            $orderNumber = $originalOrderNumber . $counter;
            $counter++;
        }

        return $orderNumber;
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = static::generateOrderNumber();
            }
        });
    }
}
