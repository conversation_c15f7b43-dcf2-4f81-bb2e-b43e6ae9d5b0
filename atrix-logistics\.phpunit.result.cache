{"version": 1, "defects": {"Tests\\Feature\\CustomerQuoteTest::customer_can_view_quote_creation_form": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_shipping_quote_request": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_product_quote_request": 7, "Tests\\Feature\\CustomerQuoteTest::shipping_quote_validation_fails_with_missing_required_fields": 7, "Tests\\Feature\\CustomerQuoteTest::product_quote_validation_fails_with_missing_products": 7, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_json_response": 7, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_validation_errors": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_lookup_their_quotes": 7, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_lookup_other_customers_quotes": 7, "Tests\\Feature\\CustomerQuoteTest::quote_lookup_fails_with_invalid_data": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_their_quote_details": 7, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_view_other_customers_quotes": 7, "Tests\\Feature\\LiveChatSystemTest::visitor_can_start_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_can_send_message": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_can_get_messages": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_view_live_chat_dashboard": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_assign_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_send_message_to_visitor": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_close_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_get_live_chat_stats": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_cannot_access_admin_routes": 8, "Tests\\Feature\\CartTest::user_can_add_product_to_cart": 7, "Tests\\Feature\\CartTest::user_cannot_add_product_without_authentication": 8, "Tests\\Feature\\CartTest::user_cannot_add_more_than_available_stock": 8, "Tests\\Feature\\CartTest::user_can_update_cart_item_quantity": 8, "Tests\\Feature\\CartTest::user_can_remove_item_by_setting_quantity_to_zero": 8, "Tests\\Feature\\CartTest::user_can_remove_product_from_cart": 8, "Tests\\Feature\\CartTest::user_can_clear_entire_cart": 8, "Tests\\Feature\\CartTest::user_can_get_cart_count": 8, "Tests\\Feature\\CartTest::user_can_save_item_for_later": 8, "Tests\\Feature\\CartTest::cart_totals_are_calculated_correctly": 8, "Tests\\Feature\\CartTest::cart_view_displays_correctly": 8, "Tests\\Feature\\CheckoutTest::user_can_view_checkout_page_with_items_in_cart": 8, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_with_empty_cart": 8, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_without_authentication": 7, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_manual_payment": 8, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_paypal_payment": 8, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_stripe_payment": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_address": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_payment_method": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_when_user_not_authenticated": 7, "Tests\\Feature\\CheckoutTest::checkout_fails_with_empty_cart": 8, "Tests\\Feature\\CheckoutTest::user_can_view_order_confirmation": 7, "Tests\\Feature\\CheckoutTest::user_cannot_view_other_users_order_confirmation": 8, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_manual_payment": 7, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_contacts": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_newsletter_subscribers": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_update_contact_status": 8, "Tests\\Feature\\CommunicationsSystemTest::newsletter_unsubscribe_works": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_bulk_delete_contacts": 8, "Tests\\Feature\\CustomerQuoteTest::checkbox_fields_are_properly_validated_and_processed": 7, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_paypal_payment": 8, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_stripe_payment": 8, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 8}, "times": {"Tests\\Unit\\ImageProcessingServiceTest::it_can_validate_valid_image": 0.038, "Tests\\Unit\\ImageProcessingServiceTest::it_rejects_oversized_images": 0.002, "Tests\\Unit\\ImageProcessingServiceTest::it_generates_unique_filenames": 0.004, "Tests\\Feature\\CartTest::user_can_add_product_to_cart": 0.02, "Tests\\Feature\\CartTest::user_cannot_add_product_without_authentication": 0.005, "Tests\\Feature\\CartTest::user_cannot_add_more_than_available_stock": 0.011, "Tests\\Feature\\CartTest::user_can_update_cart_item_quantity": 0.01, "Tests\\Feature\\CartTest::user_can_remove_item_by_setting_quantity_to_zero": 0.01, "Tests\\Feature\\CartTest::user_can_remove_product_from_cart": 0.008, "Tests\\Feature\\CartTest::user_can_clear_entire_cart": 0.01, "Tests\\Feature\\CartTest::user_can_get_cart_count": 0.006, "Tests\\Feature\\CartTest::user_can_save_item_for_later": 0.035, "Tests\\Feature\\CartTest::cart_totals_are_calculated_correctly": 0.007, "Tests\\Feature\\CartTest::cart_view_displays_correctly": 0.023, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_manual_payment": 0.013, "Tests\\Feature\\CheckoutTest::user_can_view_checkout_page_with_items_in_cart": 0.024, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_with_empty_cart": 0.006, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_without_authentication": 0.066, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_manual_payment": 4.044, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.004, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 0.066, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.035, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.206, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 0.007, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.008, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.013, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 0.02, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.016, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.011, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.211, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.008, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.212, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.228, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.214, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 0.008, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 0.007, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 0.011, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.006, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_paypal_payment": 0.012, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_stripe_payment": 0.013, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_address": 0.009, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_payment_method": 0.007, "Tests\\Feature\\CheckoutTest::checkout_fails_when_user_not_authenticated": 0.012, "Tests\\Feature\\CheckoutTest::checkout_fails_with_empty_cart": 0.007, "Tests\\Feature\\CheckoutTest::user_can_view_order_confirmation": 1.727, "Tests\\Feature\\CheckoutTest::user_cannot_view_other_users_order_confirmation": 0.008, "Tests\\Feature\\CommunicationsSystemTest::contact_form_submission_works": 0.02, "Tests\\Feature\\CommunicationsSystemTest::newsletter_subscription_works": 0.004, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_contacts": 0.001, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_newsletter_subscribers": 0.001, "Tests\\Feature\\CommunicationsSystemTest::admin_can_update_contact_status": 0.001, "Tests\\Feature\\CommunicationsSystemTest::newsletter_unsubscribe_works": 0.001, "Tests\\Feature\\CommunicationsSystemTest::contact_with_newsletter_subscription_works": 0.005, "Tests\\Feature\\CommunicationsSystemTest::admin_can_bulk_delete_contacts": 0.001, "Tests\\Feature\\CommunicationsSystemTest::admin_can_import_newsletter_subscribers": 0.005, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_quote_creation_form": 0.044, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_shipping_quote_request": 0.02, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_product_quote_request": 0.011, "Tests\\Feature\\CustomerQuoteTest::shipping_quote_validation_fails_with_missing_required_fields": 0.01, "Tests\\Feature\\CustomerQuoteTest::product_quote_validation_fails_with_missing_products": 0.005, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_json_response": 0.009, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_validation_errors": 0.007, "Tests\\Feature\\CustomerQuoteTest::customer_can_lookup_their_quotes": 0.009, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_lookup_other_customers_quotes": 0.007, "Tests\\Feature\\CustomerQuoteTest::quote_lookup_fails_with_invalid_data": 0.004, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_their_quote_details": 0.012, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_view_other_customers_quotes": 0.009, "Tests\\Feature\\CustomerQuoteTest::checkbox_fields_are_properly_validated_and_processed": 0.011, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_paypal_payment": 0.012, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_stripe_payment": 0.012, "Tests\\Feature\\EcommerceFlowTest::user_can_edit_cart_multiple_times_before_checkout": 0.03, "Tests\\Feature\\EcommerceFlowTest::stock_management_works_correctly_during_checkout": 0.022, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.195, "Tests\\Feature\\LiveChatSystemTest::visitor_can_start_chat_session": 0.012, "Tests\\Feature\\LiveChatSystemTest::visitor_can_send_message": 0.015, "Tests\\Feature\\LiveChatSystemTest::visitor_can_get_messages": 0.014, "Tests\\Feature\\LiveChatSystemTest::admin_can_view_live_chat_dashboard": 0.033, "Tests\\Feature\\LiveChatSystemTest::admin_can_assign_chat_session": 0.016, "Tests\\Feature\\LiveChatSystemTest::admin_can_send_message_to_visitor": 0.014, "Tests\\Feature\\LiveChatSystemTest::admin_can_close_chat_session": 0.011, "Tests\\Feature\\LiveChatSystemTest::admin_can_get_live_chat_stats": 0.073, "Tests\\Feature\\LiveChatSystemTest::visitor_cannot_access_admin_routes": 0.014, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 0.083, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 0.021, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 0.015, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 0.017, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 0.018}}